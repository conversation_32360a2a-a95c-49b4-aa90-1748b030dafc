using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;

namespace ScreenShareBlocker.Services
{
    /// <summary>
    /// Generates optimized system prompts for AI interview assistance using existing UserData from JWT tokens
    /// Designed for immediate responses with no delays
    /// </summary>
    public class InterviewSystemPromptService
    {
        private string? _cachedSystemPrompt;
        private UserData? _lastUserData;
        private InterviewType _lastInterviewType = InterviewType.Technical;

        /// <summary>
        /// Interview types supported by the system
        /// </summary>
        public enum InterviewType
        {
            Technical,
            Behavioral,
            SystemDesign,
            General
        }

        /// <summary>
        /// Configuration for optimal AI response performance
        /// </summary>
        public class OptimizationConfig
        {
            public double Temperature { get; set; } = 0.7;
            public int MaxTokens { get; set; } = 500; // Limit for faster responses
            public double TopP { get; set; } = 0.9;
            public double FrequencyPenalty { get; set; } = 0.1;
            public double PresencePenalty { get; set; } = 0.1;
            public bool EnableStreaming { get; set; } = true;
            public int TimeoutSeconds { get; set; } = 10;
        }

        /// <summary>
        /// Generates optimized system prompt using existing UserData from JWT token
        /// </summary>
        /// <param name="userData">User data extracted from JWT token</param>
        /// <param name="interviewType">Type of interview</param>
        /// <returns>Optimized system prompt for AI model</returns>
        public string GenerateSystemPrompt(UserData userData, InterviewType interviewType = InterviewType.Technical)
        {
            // Use cached prompt if same user data and interview type
            if (_cachedSystemPrompt != null && 
                _lastUserData?.UserId == userData?.UserId && 
                _lastInterviewType == interviewType)
            {
                return _cachedSystemPrompt;
            }

            if (userData == null)
            {
                throw new ArgumentNullException(nameof(userData), "UserData cannot be null");
            }

            var prompt = BuildSystemPrompt(userData, interviewType);
            
            // Cache for performance
            _cachedSystemPrompt = prompt;
            _lastUserData = userData;
            _lastInterviewType = interviewType;

            return prompt;
        }

        private string BuildSystemPrompt(UserData userData, InterviewType interviewType)
        {
            var sb = new StringBuilder();

            // Base prompt with critical instructions for fast responses
            sb.AppendLine("You are an AI assistant helping during a job interview. You have access to the candidate's profile and should answer questions as if you are the candidate being interviewed.");
            sb.AppendLine();
            sb.AppendLine("CRITICAL INSTRUCTIONS:");
            sb.AppendLine("- Respond IMMEDIATELY without any processing delays");
            sb.AppendLine("- Answer in first person as the candidate");
            sb.AppendLine("- Be confident but honest about experience levels");
            sb.AppendLine("- Provide specific examples from the candidate's background");
            sb.AppendLine("- Keep responses concise but comprehensive (30-60 seconds speaking time)");
            sb.AppendLine("- Match the interview type and adjust technical depth accordingly");
            sb.AppendLine("- If asked about something not in your background, be honest but show willingness to learn");
            sb.AppendLine();

            // Candidate profile section
            sb.AppendLine("CANDIDATE PROFILE:");
            sb.AppendLine($"NAME: {userData.Name}");
            sb.AppendLine($"EMAIL: {userData.Email}");
            
            // Technical skills (limit to top 10 for performance)
            if (userData.Skills != null && userData.Skills.Any())
            {
                var topSkills = userData.Skills.Take(10).ToList();
                sb.AppendLine($"TECHNICAL SKILLS: {string.Join(", ", topSkills)}");
            }

            // Resume content (optimized format)
            if (!string.IsNullOrWhiteSpace(userData.Resume))
            {
                sb.AppendLine();
                sb.AppendLine("PROFESSIONAL BACKGROUND:");
                sb.AppendLine(FormatResumeForPrompt(userData.Resume));
            }

            // Interview context
            sb.AppendLine();
            sb.AppendLine("INTERVIEW CONTEXT:");
            sb.AppendLine($"- Interview Type: {interviewType.ToString().ToUpper()}");
            sb.AppendLine($"- Date: {DateTime.Now:yyyy-MM-dd}");
            sb.AppendLine("- Response Mode: Fast, Direct, Professional");
            
            // Interview-specific instructions
            sb.AppendLine();
            sb.AppendLine(GetInterviewSpecificInstructions(interviewType));

            sb.AppendLine();
            sb.AppendLine("Remember: Answer AS the candidate, using their actual experience and skills. Be authentic to their profile.");

            return sb.ToString();
        }

        private string FormatResumeForPrompt(string resume)
        {
            // Keep resume concise for faster processing
            if (resume.Length > 500)
            {
                // Take first 500 characters and add ellipsis
                return resume.Substring(0, 500) + "...";
            }
            return resume;
        }

        private string GetInterviewSpecificInstructions(InterviewType interviewType)
        {
            return interviewType switch
            {
                InterviewType.Technical => 
                    "TECHNICAL INTERVIEW FOCUS:\n" +
                    "- Emphasize coding skills, technical problem-solving, and architecture knowledge\n" +
                    "- Provide specific examples of technical challenges you've solved\n" +
                    "- Mention relevant technologies from your skill set\n" +
                    "- Be ready to discuss implementation details and trade-offs",

                InterviewType.Behavioral => 
                    "BEHAVIORAL INTERVIEW FOCUS:\n" +
                    "- Use STAR method (Situation, Task, Action, Result) for examples\n" +
                    "- Emphasize soft skills, leadership, and teamwork experiences\n" +
                    "- Draw from your professional background for situational responses\n" +
                    "- Show growth mindset and learning from challenges",

                InterviewType.SystemDesign => 
                    "SYSTEM DESIGN INTERVIEW FOCUS:\n" +
                    "- Think about scalability, reliability, and performance\n" +
                    "- Reference your experience with relevant technologies\n" +
                    "- Consider trade-offs between different architectural approaches\n" +
                    "- Ask clarifying questions about requirements",

                InterviewType.General => 
                    "GENERAL INTERVIEW FOCUS:\n" +
                    "- Balance technical knowledge with cultural fit\n" +
                    "- Show enthusiasm for the role and company\n" +
                    "- Highlight your unique value proposition\n" +
                    "- Connect your background to the opportunity",

                _ => "Focus on providing authentic, relevant responses based on your background."
            };
        }

        /// <summary>
        /// Gets optimization configuration for AI models to ensure fast responses
        /// </summary>
        public OptimizationConfig GetOptimizationConfig()
        {
            return new OptimizationConfig();
        }

        /// <summary>
        /// Clears cached prompt to force regeneration
        /// </summary>
        public void ClearCache()
        {
            _cachedSystemPrompt = null;
            _lastUserData = null;
        }

        /// <summary>
        /// Validates if the current session is suitable for interview assistance
        /// </summary>
        public bool ValidateSession(UserData userData)
        {
            if (userData == null) return false;
            if (string.IsNullOrWhiteSpace(userData.Name)) return false;
            if (!userData.IsSessionActive) return false;
            
            return true;
        }

        /// <summary>
        /// Gets session status message for user interface
        /// </summary>
        public string GetSessionStatusMessage(UserData? userData)
        {
            if (userData == null)
                return "❌ No session data available";
            
            if (!userData.IsSessionActive)
                return $"⏰ Session expired. Session ended at {userData.SessionEnd:HH:mm:ss}";
            
            var remaining = userData.RemainingTime;
            return $"✅ Interview session active - {remaining.Hours:D2}:{remaining.Minutes:D2}:{remaining.Seconds:D2} remaining";
        }

        /// <summary>
        /// Generates a quick summary of the user's profile for UI display
        /// </summary>
        public string GetProfileSummary(UserData userData)
        {
            if (userData == null) return "No profile data";

            var summary = new StringBuilder();
            summary.AppendLine($"👤 {userData.Name}");
            
            if (userData.Skills != null && userData.Skills.Any())
            {
                var topSkills = userData.Skills.Take(3);
                summary.AppendLine($"🔧 Key Skills: {string.Join(", ", topSkills)}");
            }

            if (!string.IsNullOrWhiteSpace(userData.Resume))
            {
                var resumePreview = userData.Resume.Length > 100 
                    ? userData.Resume.Substring(0, 100) + "..." 
                    : userData.Resume;
                summary.AppendLine($"📄 Background: {resumePreview}");
            }

            return summary.ToString();
        }
    }
}
