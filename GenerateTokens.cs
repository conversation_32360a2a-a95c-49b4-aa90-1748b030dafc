using System;
using ScreenShareBlocker;

namespace ScreenShareBlocker
{
    /// <summary>
    /// Console utility to generate JWT tokens for testing
    /// Run this as a separate console app to get tokens
    /// </summary>
    class TokenGeneratorConsole
    {
        static void MainTokens(string[] args)
        {
            Console.WriteLine("=== INTERVIEW AI ASSISTANT - JWT TOKEN GENERATOR ===\n");
            Console.WriteLine("This utility generates sample JWT tokens for testing the Interview AI Assistant.\n");
            
            try
            {
                // Generate all sample tokens
                TokenGenerator.PrintSampleTokens();
                
                Console.WriteLine("\n=== HOW TO TEST ===");
                Console.WriteLine("1. Copy any token above");
                Console.WriteLine("2. Run the Interview AI Assistant application");
                Console.WriteLine("3. Paste the token or click a sample token");
                Console.WriteLine("4. Click 'Start Session' to proceed");
                
                Console.WriteLine("\n=== TEST SCENARIOS ===");
                Console.WriteLine("🟢 Active Token: Will launch the full interview assistant");
                Console.WriteLine("🔴 Expired Token: Will show 'Session Expired' message");
                Console.WriteLine("🟡 Future Token: Will show 'Session not started yet' message");
                
                Console.WriteLine("\n=== INDIVIDUAL TOKENS ===");
                Console.WriteLine("\nActive Token:");
                Console.WriteLine(TokenGenerator.GenerateDummyToken("active"));
                
                Console.WriteLine("\nExpired Token:");
                Console.WriteLine(TokenGenerator.GenerateDummyToken("expired"));
                
                Console.WriteLine("\nFuture Token:");
                Console.WriteLine(TokenGenerator.GenerateDummyToken("future"));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            
            Console.WriteLine("\n\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
