using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;

namespace ScreenShareBlocker
{
    public partial class TokenInputWindow : Window
    {
        public SessionManager SessionManager { get; private set; }
        public bool IsSessionValid { get; private set; } = false;

        public TokenInputWindow()
        {
            InitializeComponent();
            SessionManager = new SessionManager();
            LoadSampleTokens();
        }

        private void LoadSampleTokens()
        {
            try
            {
                ActiveTokenTextBox.Text = TokenGenerator.GenerateDummyToken("active");
                ExpiredTokenTextBox.Text = TokenGenerator.GenerateDummyToken("expired");
                FutureTokenTextBox.Text = TokenGenerator.GenerateDummyToken("future");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error loading sample tokens: {ex.Message}", Brushes.Red);
            }
        }

        private void ValidateButton_Click(object sender, RoutedEventArgs e)
        {
            string token = TokenTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(token))
            {
                UpdateStatus("Please enter a session token.", Brushes.Orange);
                return;
            }

            try
            {
                bool isValid = SessionManager.ValidateToken(token);
                
                if (isValid)
                {
                    var userData = SessionManager.UserData;
                    if (userData != null)
                    {
                        if (userData.IsSessionActive)
                        {
                            UpdateStatus($"✅ Session validated! Welcome, {userData.Name}", Brushes.LightGreen);
                            IsSessionValid = true;
                            
                            // Launch the main Interview AI Assistant window
                            var mainWindow = new MainWindow(SessionManager);
                            mainWindow.Show();
                            
                            // Close this token input window
                            this.Close();
                        }
                        else
                        {
                            // Check if session hasn't started yet
                            if (DateTime.UtcNow < userData.SessionStart)
                            {
                                var timeUntilStart = userData.SessionStart - DateTime.UtcNow;
                                UpdateStatus($"⏳ Session hasn't started yet. Starts in {timeUntilStart.Hours:D2}:{timeUntilStart.Minutes:D2}:{timeUntilStart.Seconds:D2}", Brushes.Orange);
                            }
                            else
                            {
                                UpdateStatus($"❌ Session has expired for {userData.Name}. Please contact support for a new token.", Brushes.Red);
                            }
                        }
                    }
                }
                else
                {
                    UpdateStatus("❌ Invalid token. Please check your token and try again.", Brushes.Red);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"❌ Error validating token: {ex.Message}", Brushes.Red);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            TokenTextBox.Clear();
            StatusTextBlock.Text = "";
            TokenTextBox.Focus();
        }

        private void ExitButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void SampleToken_Click(object sender, MouseButtonEventArgs e)
        {
            if (sender is System.Windows.Controls.Border border && border.Tag is string scenario)
            {
                try
                {
                    string token = TokenGenerator.GenerateDummyToken(scenario);
                    TokenTextBox.Text = token;
                    
                    string scenarioText = scenario switch
                    {
                        "active" => "Active session token loaded ✅",
                        "expired" => "Expired session token loaded ⚠️",
                        "future" => "Future session token loaded ⏳",
                        _ => "Sample token loaded"
                    };
                    
                    UpdateStatus($"{scenarioText} Click 'Start Session' to proceed.", Brushes.LightBlue);
                }
                catch (Exception ex)
                {
                    UpdateStatus($"Error loading token: {ex.Message}", Brushes.Red);
                }
            }
        }

        private void UpdateStatus(string message, Brush color)
        {
            StatusTextBlock.Text = message;
            StatusTextBlock.Foreground = color;
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // Set focus to token input
            TokenTextBox.Focus();
        }

        private void TokenTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                ValidateButton_Click(sender, e);
            }
        }
    }
}
