<Window x:Class="ScreenShareBlocker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Interview AI Assistant" Height="720" Width="1000"
        Background="Transparent" AllowsTransparency="True" WindowStyle="None"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Border Background="#90000000" Margin="10">
            <StackPanel VerticalAlignment="Top" HorizontalAlignment="Stretch" Margin="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10,5,10,15">
                <Button Name="StartListeningButton" Content="🎧 Listen" Width="120" Height="40" Margin="6" 
                        Click="StartServiceButton_Click" FontSize="13" FontWeight="SemiBold" 
                        Background="#2196F3" Foreground="White"/>
                <Button Name="StopListeningButton" Content="🔇 Stop" Width="100" Height="40" Margin="6" 
                        Click="StopServiceButton_Click" FontSize="13" FontWeight="SemiBold" 
                        Background="#FF5722" Foreground="White"/>
                <Button Name="AnalyzeButton" Content="🔍 Answer" Width="110" Height="40" Margin="6" 
                        Click="AnalyzeButton_Click" FontSize="13" FontWeight="SemiBold" 
                        Background="#FF9800" Foreground="White"/>
                <Button Name="ScreenCaptureButton" Content="📸 Capture" Width="110" Height="40" Margin="6" 
                        Click="ScreenCaptureButton_Click" Background="#4CAF50" Foreground="White" 
                        FontSize="13" FontWeight="SemiBold"/>
                <Button Name="ClearChatButton" Content="🗑️ Clear" Width="100" Height="40" Margin="6" 
                        Click="ClearChatButton_Click" FontSize="13" FontWeight="SemiBold" 
                        Background="#9E9E9E" Foreground="White"/>
            </StackPanel>
                
                <!-- <Border Background="#4CAF50" CornerRadius="5">
                    <Button Name="ToggleProtectionButton" Content="Toggle Protection" 
                            Width="200" Height="40" FontSize="14" Click="ToggleProtection_Click"
                            Background="Transparent" Foreground="White" BorderThickness="0"/>
                </Border>

                <TextBlock Name="StatusTextBlock" Text="Status: Protected" 
                           FontSize="12" HorizontalAlignment="Center" Margin="0,10,0,0"
                           Foreground="LightGreen" FontWeight="Bold"/> -->
                
                <!-- AI Assistant Interface -->
                <Border Background="#50FFFFFF" Padding="15" Margin="0,5,0,0">
                    <StackPanel>
                        <TextBlock Text="📚 Interview AI Assistant" FontSize="20" FontWeight="Bold" 
                                   Margin="0,0,0,10" Foreground="White" HorizontalAlignment="Center"/>
                        
                        <ScrollViewer Name="ChatScrollViewer" Height="380" Margin="0,0,0,10"
                                      VerticalScrollBarVisibility="Auto" Background="#10000000" 
                                      BorderBrush="#30FFFFFF" BorderThickness="1" Padding="10">
                            <ItemsControl Name="ChatItemsControl" ItemsSource="{Binding ChatMessages, RelativeSource={RelativeSource AncestorType=Window}}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{Binding BackgroundColor}" 
                                                Margin="5" Padding="20" 
                                                BorderBrush="#20FFFFFF" BorderThickness="1">
                                            <TextBlock Text="{Binding Message}" TextWrapping="Wrap" 
                                                       Foreground="{Binding TextColor}" FontSize="15" 
                                                       FontFamily="Segoe UI" LineHeight="22"/>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                        
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Stretch">
                            <Border Background="#80FFFFFF" Margin="0,0,10,0" Padding="8" BorderThickness="1" BorderBrush="#40FFFFFF">
                                <TextBox Name="UserInputTextBox" Width="320" Height="35" FontSize="14"
                                         Padding="8" BorderThickness="0" Background="#F0FFFFFF" 
                                         Foreground="#333333" KeyDown="UserInputTextBox_KeyDown" 
                                         FontFamily="Segoe UI" VerticalContentAlignment="Center" 
                                         Text="Type your message here..." 
                                         Opacity="1" FontWeight="Normal"/>
                            </Border>
                            <Border Background="#2196F3" Padding="3">
                                <Button Name="SendChatButton" Content="📤 Send" Width="90" Height="35" 
                                        FontSize="14" Click="SendChatButton_Click" FontWeight="SemiBold"
                                        Background="Transparent" Foreground="White" BorderThickness="0"/>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- Close Button -->
        <!-- <Button Content="X" Width="30" Height="30"
                HorizontalAlignment="Right" VerticalAlignment="Top"
                Margin="10"
                Background="#FF5722" Foreground="White" FontWeight="Bold"
                Click="CloseButton_Click"/> -->
        <Button Content="X" Width="35" Height="35" 
                HorizontalAlignment="Right" VerticalAlignment="Top" 
                Margin="15" FontSize="16"
                Background="#FF4444" Foreground="White" FontWeight="Bold"
                BorderThickness="0" Opacity="0.9"
                Click="CloseButton_Click">
            <Button.Style>
                <Style TargetType="Button">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}" 
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}">
                                    <ContentPresenter HorizontalAlignment="Center" 
                                                      VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#FF6666"/>
                            <Setter Property="Opacity" Value="1"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Button.Style>
        </Button>
    </Grid>
</Window>

