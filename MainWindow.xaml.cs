using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using NAudio.Wave;
using Microsoft.CognitiveServices.Speech;
using Microsoft.CognitiveServices.Speech.Audio;
using System.Collections.Concurrent;
using NAudio.CoreAudioApi;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows.Threading;
using System.Windows.Input;
using System.Net.Http;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using ScreenShareBlocker.Services;

namespace ScreenShareBlocker
{
    public class ChatMessage
    {
        public string Message { get; set; }
        public string BackgroundColor { get; set; }
        public string TextColor { get; set; }
    }

    public partial class MainWindow : Window
    {
        private readonly SessionManager? _sessionManager;
        private readonly System.Windows.Threading.DispatcherTimer? _sessionTimer;
        private AudioTranscriptionService? _audioService;
        [DllImport("user32.dll")]
        static extern bool SetWindowDisplayAffinity(IntPtr hWnd, uint dwAffinity);

        const uint WDA_NONE = 0x00;
        const uint WDA_EXCLUDEFROMCAPTURE = 0x11;

        private bool isProtected = true;
        private IntPtr windowHandle;
        private string _latestTranscription = string.Empty;
        private ObservableCollection<ChatMessage> _chatMessages = new ObservableCollection<ChatMessage>();
        private readonly InterviewSystemPromptService _promptService = new InterviewSystemPromptService();

        public MainWindow() : this(null)
        {
        }
        
        public MainWindow(SessionManager? sessionManager)
        {
            InitializeComponent();
            _sessionManager = sessionManager;
            ChatItemsControl.ItemsSource = _chatMessages;
            
            // Setup session timer if we have session management
            if (_sessionManager?.HasValidSession == true)
            {
                _sessionTimer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(1)
                };
                _sessionTimer.Tick += SessionTimer_Tick;
                _sessionTimer.Start();
                
                // Show welcome message with session info
                var userData = _sessionManager.UserData;
                if (userData != null)
                {
                    AddChatMessage($"🎯 SESSION STARTED - Welcome {userData.Name}! Session expires at {userData.SessionEnd:HH:mm:ss UTC}", "#4CAF50", "#FFFFFF");
                    AddChatMessage($"👤 User: {userData.Name} ({userData.Email})", "#2196F3", "#FFFFFF");
                    if (userData.Skills.Any())
                    {
                        AddChatMessage($"🛠️ Skills: {string.Join(", ", userData.Skills)}", "#9C27B0", "#FFFFFF");
                    }
                }
            }
            
            // Wait for window to be fully loaded before applying protection
            this.Loaded += MainWindow_Loaded;
            // Setup text input placeholder behavior
            SetupTextInputPlaceholder();
            // Initialization of AzureSpeech and AudioCapture moved to background service
        }

        // --- Background Service Example ---
        private CancellationTokenSource _backgroundServiceCts;

        private async void StartBackgroundService()
        {
            if (_audioService != null)
            {
                return; // Service already running
            }
            // TODO: Store keys securely
            var speechKey = "7BYgJa31VkLQfn2DDyU1qJhzn267vcx00OtM7pbacLzjbIwhRk1KJQQJ99BGACYeBjFXJ3w3AAAYACOGVFI9";
            var speechRegion = "eastus";
            _audioService = new AudioTranscriptionService(
                speechKey,
                speechRegion,
                logAction: null, // No log messages in chat
                transcriptAction: msg => Dispatcher.Invoke(() => UpdateLiveTranscription(msg)),
                finalTranscriptAction: (finalText) =>
                {
                    // Store the latest final transcription for the analyze button
                    _latestTranscription = finalText;
                    // Don't show separate final messages - live transcription is already final
                }
            );
            try
            {
                await _audioService.StartAsync();
                AddChatMessage("🎧 LISTENING STARTED - Speak your question now!", "#4CAF50", "#FFFFFF");
            }
            catch (Exception ex)
            {
                AddChatMessage($"❌ Failed to start: {ex.Message}", "#FFEBEE", "Red");
                _audioService = null;
            }
        }

        private async void StopBackgroundService()
        {
            if (_audioService != null)
            {
                await _audioService.StopAsync();
                _audioService.Dispose();
                _audioService = null;
                AddChatMessage("🔇 LISTENING STOPPED", "#FF5722", "#FFFFFF");
            }
        }

        // BackgroundServiceLoop removed; logic is now in AudioTranscriptionService

        private void StartServiceButton_Click(object sender, RoutedEventArgs e)
        {
            StartBackgroundService();
        }

        private void StopServiceButton_Click(object sender, RoutedEventArgs e)
        {
            StopBackgroundService();
        }
        // Audio/speech logic moved to AudioTranscriptionService

        private void OnRecognized(object? sender, SpeechRecognitionEventArgs e)
        {
            if (e.Result.Reason == ResultReason.RecognizedSpeech && !string.IsNullOrEmpty(e.Result.Text))
            {
                // Check if this is a final recognized speech
                var finalText = e.Result.Text;
                
                // Filter filler words from final speech
                var cleanText = FilterFillerWords(finalText);
                if (string.IsNullOrWhiteSpace(cleanText)) return;
                
                _latestTranscription = cleanText;
                Dispatcher.Invoke(async () =>
                {
                    AddChatMessage($"Mic (Final): {cleanText}", "#E3F2FD", "#1976D2");
                    await StreamChatGptResponse(cleanText);
                });
            }
        }

        private string GetLatestTranscriptionText()
        {
            // First check if we have a stored transcription in MainWindow
            if (!string.IsNullOrWhiteSpace(_latestTranscription))
                return _latestTranscription;
            
            // If not, try to get it from the audio service
            if (_audioService != null && !string.IsNullOrWhiteSpace(_audioService.LatestFinalTranscription))
                return _audioService.LatestFinalTranscription;
            
            return string.Empty;
        }

        private void OnRecognizing(object? sender, SpeechRecognitionEventArgs e)
        {
            // Optional: Show partial recognition results
            if (!string.IsNullOrEmpty(e.Result.Text))
            {
                Dispatcher.Invoke(() =>
                {
                    // You could show partial results here if desired
                });
            }
        }
        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Get window handle
            windowHandle = new WindowInteropHelper(this).Handle;

            // Apply initial protection
            ApplyProtection(true);

            // Check Windows version
            CheckWindowsVersion();
        }

        private void ApplyProtection(bool protect)
        {
            if (windowHandle == IntPtr.Zero)
                return;

            uint affinity = protect ? WDA_EXCLUDEFROMCAPTURE : WDA_NONE;
            bool success = SetWindowDisplayAffinity(windowHandle, affinity);

            isProtected = protect;

            // if (success)
            // {
            //     StatusTextBlock.Text = protect ? "Status: Protected ✅" : "Status: Not Protected ❌";
            //     StatusTextBlock.Foreground = protect ? System.Windows.Media.Brushes.Green : System.Windows.Media.Brushes.Red;
            // }
            // else
            // {
            //     StatusTextBlock.Text = "Status: Error applying protection ⚠️";
            //     StatusTextBlock.Foreground = System.Windows.Media.Brushes.Orange;
            //     System.Windows.MessageBox.Show("Failed to apply window display affinity. This feature requires Windows 10 version 2004 or later.", 
            //                    "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            // }
        }

        private void ToggleProtection_Click(object sender, RoutedEventArgs e)
        {
            ApplyProtection(!isProtected);
        }

        private void CheckWindowsVersion()
        {
            var version = Environment.OSVersion.Version;

            // Windows 10 version 2004 is build 19041
            bool isSupported = version.Major >= 10 && version.Build >= 19041;

            if (!isSupported)
            {
                System.Windows.MessageBox.Show($"Current Windows version: {version}\n\n" +
                               "This feature requires Windows 10 version 2004 (build 19041) or later.\n" +
                               "The protection may not work on this system.",
                               "Version Warning", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        // --- ChatGPT Integration ---
        private const string OpenAIApiUrl = "https://api.openai.com/v1/chat/completions";
        // TODO: Store your API key securely, e.g., in environment variable or config
        private string openAIApiKey = "********************************************************************************************************************************************************************";

        private async void SendChatButton_Click(object sender, RoutedEventArgs e)
        {
            string userMessage = UserInputTextBox.Text.Trim();
            if (string.IsNullOrEmpty(userMessage) || userMessage == "Type your message here...") return;

            // Filter filler words from user input
            string cleanMessage = FilterFillerWords(userMessage);
            if (string.IsNullOrWhiteSpace(cleanMessage)) return;

            AddChatMessage($"You: {cleanMessage}", "#2E7D32", "#FFFFFF");
            UserInputTextBox.Text = "";
            UserInputTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x80, 0x80, 0x80));

            await StreamChatGptResponse(cleanMessage);
        }

        private async Task StreamChatGptResponse(string userMessage)
        {
            await StreamChatGptResponseInternal(userMessage, "gpt-4.1-nano");
        }
        
        private async Task StreamChatGptResponseFast(string userMessage)
        {
            await StreamChatGptResponseInternal(userMessage, "gpt-4.1-nano");
        }
        
        private async Task StreamChatGptResponseInternal(string userMessage, string model)
        {
            try
            {
                using (var client = new System.Net.Http.HttpClient())
                {
                    // Faster timeout
                    client.Timeout = TimeSpan.FromSeconds(30); // Faster timeout
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {openAIApiKey}");
                    // Build system prompt using session user data when available
                    string systemPrompt = "You are an AI assistant. Answer concisely and professionally.";
                    var userData = _sessionManager?.UserData;
                    if (userData != null && _promptService.ValidateSession(userData))
                    {
                        systemPrompt = _promptService.GenerateSystemPrompt(userData, InterviewSystemPromptService.InterviewType.Technical);
                    }         
                    var opt = _promptService.GetOptimizationConfig();           
                    var requestBody = new
                    {
                        model = model,
                        messages = new[] {
                            new { role = "system", content = systemPrompt },
                            new { role = "user", content = userMessage }
                        },
                        stream = true,
                        max_tokens = 500, // Limit tokens for faster response
                        temperature = 0.7
                    };
                    var json = System.Text.Json.JsonSerializer.Serialize(requestBody);
                    var content = new System.Net.Http.StringContent(json, System.Text.Encoding.UTF8, "application/json");
                    var request = new HttpRequestMessage(HttpMethod.Post, OpenAIApiUrl) { Content = content };
                    using var response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                    response.EnsureSuccessStatusCode();
                    using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new System.IO.StreamReader(stream);

                    string partial = "";
                    bool isFirstToken = true;
                    while (!reader.EndOfStream)
                    {
                        var line = await reader.ReadLineAsync();
                        if (string.IsNullOrWhiteSpace(line) || !line.StartsWith("data:")) continue;

                        var jsonLine = line.Substring(5).Trim();
                        if (jsonLine == "[DONE]") break;

                        try
                        {
                            using var doc = System.Text.Json.JsonDocument.Parse(jsonLine);
                            if (doc.RootElement.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
                            {
                                var choice = choices[0];
                                if (choice.TryGetProperty("delta", out var delta) && delta.TryGetProperty("content", out var contentProp))
                                {
                                    var deltaText = contentProp.GetString();
                                    if (!string.IsNullOrEmpty(deltaText))
                                    {
                                        partial += deltaText;
                                        // Show partial result in chat window with faster updates
                                        Dispatcher.Invoke(() =>
                                        {
                                            // Remove last partial message if present
                                            if (_chatMessages.Count > 0 && _chatMessages[^1].Message.StartsWith("🤖 AI:"))
                                                _chatMessages.RemoveAt(_chatMessages.Count - 1);
                                            
                                            AddChatMessage($"🤖 AI: {partial}", "#F3E5F5", "#7B1FA2");
                                            
                                            // Auto-scroll after first token for immediate visual feedback
                                            if (isFirstToken)
                                            {
                                                ChatScrollViewer.ScrollToEnd();
                                                isFirstToken = false;
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        catch { /* Ignore parse errors for non-content lines */ }
                    }
                }
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => AddChatMessage($"❌ AI Error: {ex.Message}", "#FFEBEE", "Red"));
            }
        }

        private void UserInputTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                SendChatButton_Click(sender, e);
            }
        }

        private string FilterFillerWords(string text)
        {
            if (string.IsNullOrWhiteSpace(text)) return text;
            
            // Comprehensive filler words and interview hesitations
            var fillerWords = new[] {
                // Basic hesitations
                "uh", "uhh", "um", "umm", "uhhh", "ummm", "uhm",
                "ah", "ahh", "oh", "ohh", "eh", "ehh", "mm", "mmm",
                "er", "err", "erm", "urm", "hm", "hmm", "huh",
                
                // Common fillers
                "like", "you know", "I mean", "I think", "I guess",
                "sort of", "kind of", "basically", "actually", "literally",
                "honestly", "obviously", "clearly", "definitely", "probably",
                
                // Interview nervousness
                "well", "so", "okay", "ok", "right", "yeah", "yes", "yep",
                "no", "nah", "sure", "fine", "good", "great", "nice",
                "mhm", "uh-huh", "mm-hmm", "mm-kay", "alright", "all right",
                
                // False starts and repetition indicators
                "let me", "let me see", "let me think", "wait", "hold on",
                "give me a second", "one second", "just a moment",
                "what I meant", "what I mean", "sorry", "excuse me",
                
                // Thinking out loud
                "let's see", "now", "then", "next", "first", "second", "third",
                "also", "plus", "and then", "so then", "after that",
                "on the other hand", "by the way", "anyway",
                
                // Confidence fillers
                "I believe", "I feel like", "in my opinion", "personally",
                "from my perspective", "as I see it", "the way I see it",
                
                // Redundant acknowledgments
                "you see", "you understand", "you get it", "makes sense",
                "does that make sense", "if that makes sense"
            };
            
            // Split into words and sentences for better processing
            var sentences = text.Split(new[] { '.', '!', '?' }, StringSplitOptions.RemoveEmptyEntries);
            var processedSentences = new List<string>();
            
            foreach (var sentence in sentences)
            {
                var words = sentence.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                var filteredWords = new List<string>();
                
                for (int i = 0; i < words.Length; i++)
                {
                    var word = words[i];
                    var cleanWord = word.Trim(',', '.', '!', '?', ';', ':', '-', '"', '\'').ToLowerInvariant();
                    
                    // Skip filler words
                    if (fillerWords.Contains(cleanWord)) continue;
                    
                    // Handle common phrase patterns
                    if (i < words.Length - 1)
                    {
                        var twoWordPhrase = $"{cleanWord} {words[i + 1].Trim(',', '.', '!', '?', ';', ':', '-', '"', '\'').ToLowerInvariant()}";
                        if (fillerWords.Contains(twoWordPhrase))
                        {
                            i++; // Skip next word too
                            continue;
                        }
                    }
                    
                    // Handle three-word phrases
                    if (i < words.Length - 2)
                    {
                        var threeWordPhrase = $"{cleanWord} {words[i + 1].Trim(',', '.', '!', '?', ';', ':', '-', '"', '\'').ToLowerInvariant()} {words[i + 2].Trim(',', '.', '!', '?', ';', ':', '-', '"', '\'').ToLowerInvariant()}";
                        if (fillerWords.Contains(threeWordPhrase))
                        {
                            i += 2; // Skip next two words
                            continue;
                        }
                    }
                    
                    // Skip repeated words (stuttering)
                    if (filteredWords.Count > 0 && cleanWord == filteredWords.Last().Trim(',', '.', '!', '?', ';', ':', '-', '"', '\'').ToLowerInvariant())
                        continue;
                    
                    filteredWords.Add(word);
                }
                
                if (filteredWords.Count > 0)
                {
                    processedSentences.Add(string.Join(" ", filteredWords).Trim());
                }
            }
            
            var result = string.Join(". ", processedSentences.Where(s => !string.IsNullOrWhiteSpace(s)));
            
            // Final cleanup - remove extra spaces and normalize punctuation
            while (result.Contains("  "))
                result = result.Replace("  ", " ");
            
            // Clean up common speech patterns
            result = System.Text.RegularExpressions.Regex.Replace(result, @"\b(the the|a a|an an|is is|was was|are are|have have|has has|will will|can can|could could|would would|should should)\b", 
                match => match.Value.Split(' ')[0], System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            // Remove trailing incomplete thoughts
            result = System.Text.RegularExpressions.Regex.Replace(result, @"\s+(and|but|or|so|because|since|when|where|what|how|why)\s*$", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            
            return result.Trim();
        }

        private void UpdateLiveTranscription(string message)
        {
            // Only show live transcriptions (not final ones)
            if (!message.StartsWith("Live:")) return;
            
            // Extract the actual text from the live transcription
            var transcriptionText = message.Substring(6); // Remove "Live: " prefix
            
            // Filter filler words from transcription
            transcriptionText = FilterFillerWords(transcriptionText);
            
            // Store this as the latest transcription immediately (treat live as final)
            if (!string.IsNullOrWhiteSpace(transcriptionText))
            {
                _latestTranscription = transcriptionText;
            }
            
            // Remove existing live transcription
            RemoveLiveTranscription();
            
            // Add new live transcription with better styling for readability
            AddChatMessage($"🎙️ QUESTION: {transcriptionText}", "#E3F2FD", "#0D47A1");
        }
        
        private void RemoveLiveTranscription()
        {
            // Remove any existing live transcription messages
            for (int i = _chatMessages.Count - 1; i >= 0; i--)
            {
                if (_chatMessages[i].Message.StartsWith("🎙️") || _chatMessages[i].Message.StartsWith("Live:"))
                {
                    _chatMessages.RemoveAt(i);
                    break; // Only remove the most recent live transcription
                }
            }
        }

        private void AddChatMessage(string message, string backgroundColor, string textColor)
        {
            // Improve AI responses for better visibility and readability
            if (message.StartsWith("🤖 AI:") || message.StartsWith("🦾 Vision:"))
            {
                textColor = "#FFFFFF"; // Pure white text
                backgroundColor = "#263238"; // Dark blue-grey background
            }
            // Make questions more prominent
            else if (message.StartsWith("🎙️ QUESTION:"))
            {
                textColor = "#FFFFFF"; // White text
                backgroundColor = "#1565C0"; // Strong blue background
            }
            // Analysis messages
            else if (message.StartsWith("🔍 Analyzing:"))
            {
                textColor = "#FFFFFF"; // White text
                backgroundColor = "#EF6C00"; // Strong orange background
            }
            // User input
            else if (message.StartsWith("You:"))
            {
                textColor = "#FFFFFF"; // White text
                backgroundColor = "#2E7D32"; // Strong green background
            }

            _chatMessages.Add(new ChatMessage
            {
                Message = message,
                BackgroundColor = backgroundColor,
                TextColor = textColor
            });

            // Auto-scroll to bottom
            Dispatcher.BeginInvoke(new Action(() =>
            {
                ChatScrollViewer.ScrollToEnd();
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        private async void AnalyzeButton_Click(object sender, RoutedEventArgs e)
        {
            // Get the latest transcription text (including live transcription)
            string currentTranscription = GetLatestTranscriptionText();
            if (string.IsNullOrWhiteSpace(currentTranscription))
            {
                AddChatMessage("⚠️ No transcription available. Start listening and speak something first.", "#FFF3E0", "#BF360C");
                return;
            }

            // Filter filler words from transcription before analysis
            string cleanTranscription = FilterFillerWords(currentTranscription);
            if (string.IsNullOrWhiteSpace(cleanTranscription))
            {
                AddChatMessage("⚠️ No meaningful content after filtering. Please speak more clearly.", "#FFF3E0", "#BF360C");
                return;
            }

            AddChatMessage($"🔍 ANALYZING: {cleanTranscription}", "#EF6C00", "#FFFFFF");
            
            // Use a more direct and faster analysis prompt - no delay, instant response
            await StreamChatGptResponseFast($"'{cleanTranscription}'");
            
            // Don't clear the transcription - keep it available for re-analysis if needed
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void ClearChatButton_Click(object sender, RoutedEventArgs e)
        {
            _chatMessages.Clear();
        }
        
        // --- Text Input Placeholder Functionality ---
        private void SetupTextInputPlaceholder()
        {
            UserInputTextBox.GotFocus += (s, e) => 
            {
                if (UserInputTextBox.Text == "Type your message here...")
                {
                    UserInputTextBox.Text = "";
                    UserInputTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Black);
                }
            };
            
            UserInputTextBox.LostFocus += (s, e) => 
            {
                if (string.IsNullOrWhiteSpace(UserInputTextBox.Text))
                {
                    UserInputTextBox.Text = "Type your message here...";
                    UserInputTextBox.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x80, 0x80, 0x80));
                }
            };
        }
        
        // --- Screen Capture Functionality ---
        private async void ScreenCaptureButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddChatMessage("📸 CAPTURING SCREEN - Please wait...", "#2196F3", "#FFFFFF");
                
                // Temporarily minimize this window to avoid capturing it
                var currentWindowState = this.WindowState;
                this.WindowState = WindowState.Minimized;
                
                // Wait a moment for window to minimize
                await Task.Delay(500);
                
                // Capture the screen
                string screenshotPath = CaptureScreen();
                
                // Restore window
                this.WindowState = currentWindowState;
                
                if (!string.IsNullOrEmpty(screenshotPath))
                {
                    AddChatMessage("🔍 ANALYZING CODING CHALLENGE - Generating solution...", "#EF6C00", "#FFFFFF");
                    await AnalyzeScreenshotWithVision(screenshotPath);
                    
                    // Clean up the temporary file
                    try { File.Delete(screenshotPath); } catch { }
                }
                else
                {
                    AddChatMessage("❌ Failed to capture screenshot", "#FFEBEE", "Red");
                }
            }
            catch (Exception ex)
            {
                AddChatMessage($"❌ Screen capture error: {ex.Message}", "#FFEBEE", "Red");
            }
        }
        
        private string CaptureScreen()
        {
            try
            {
                // Get the screen bounds
                var bounds = System.Windows.Forms.Screen.PrimaryScreen.Bounds;
                
                // Create a bitmap to hold the screenshot
                using (var bitmap = new Bitmap(bounds.Width, bounds.Height))
                {
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        // Copy the screen to the bitmap
                        graphics.CopyFromScreen(bounds.X, bounds.Y, 0, 0, bounds.Size);
                    }
                    
                    // Save to temporary file
                    string tempPath = Path.Combine(Path.GetTempPath(), $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png");
                    bitmap.Save(tempPath, ImageFormat.Png);
                    return tempPath;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Screen capture error: {ex.Message}");
                return string.Empty;
            }
        }
        
        // --- Session Timer Functionality ---
        private void SessionTimer_Tick(object? sender, EventArgs e)
        {
            if (_sessionManager?.UserData != null)
            {
                var userData = _sessionManager.UserData;
                if (!userData.IsSessionActive)
                {
                    // Session has expired
                    _sessionTimer?.Stop();
                    AddChatMessage("⏰ SESSION EXPIRED - Your interview session has ended. The application will close.", "#FF5722", "#FFFFFF");
                    
                    // Close the application after a short delay
                    var closeTimer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(3) };
                    closeTimer.Tick += (s, args) =>
                    {
                        closeTimer.Stop();
                        Application.Current.Shutdown();
                    };
                    closeTimer.Start();
                }
                else
                {
                    // Update remaining time in window title
                    var remaining = userData.RemainingTime;
                    this.Title = $"Interview AI Assistant - {remaining.Hours:D2}:{remaining.Minutes:D2}:{remaining.Seconds:D2} remaining";
                    
                    // Show warnings at 10 minutes and 5 minutes
                    var totalMinutes = remaining.TotalMinutes;
                    if (Math.Abs(totalMinutes - 10) < 0.02) // Within 1 second of 10 minutes
                    {
                        AddChatMessage("⚠️ SESSION WARNING - 10 minutes remaining in your interview session!", "#FF9800", "#FFFFFF");
                    }
                    else if (Math.Abs(totalMinutes - 5) < 0.02) // Within 1 second of 5 minutes
                    {
                        AddChatMessage("🚨 SESSION WARNING - Only 5 minutes left! Please wrap up your session.", "#FF5722", "#FFFFFF");
                    }
                }
            }
        }
        
        private async Task AnalyzeScreenshotWithVision(string imagePath)
        {
            try
            {
                // Convert image to base64
                byte[] imageBytes = File.ReadAllBytes(imagePath);
                string base64Image = Convert.ToBase64String(imageBytes);

                // Get user preferred languages from skills
                var userData = _sessionManager?.UserData;
                string languageRequest = "Python";
                if (userData != null && userData.Skills != null && userData.Skills.Any())
                {
                    // Filter for common programming languages
                    var knownLanguages = new[] { "Python", "C#", "Java", "C++", "JavaScript", "TypeScript", "Go", "Rust", "Ruby", "Kotlin", "Swift" };
                    var userLanguages = userData.Skills.Intersect(knownLanguages, StringComparer.OrdinalIgnoreCase).ToList();
                    if (userLanguages.Any())
                    {
                        languageRequest = string.Join(" and/or ", userLanguages);
                    }
                }

                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(60); // Longer timeout for vision analysis
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {openAIApiKey}");

                    var requestBody = new
                    {
                        model = "gpt-4.1-nano", // Use GPT-4 Vision model
                        messages = new[] {
                            new {
                                role = "user",
                                content = new object[] {
                                    new {
                                        type = "text",
                                        text = $"This is a screenshot that likely contains a coding challenge, algorithm problem, or technical interview question. Please analyze it and provide:\n\n1. Identify what type of problem this is (LeetCode, algorithm, data structure, etc.)\n2. Explain the problem clearly\n3. Provide a detailed solution approach\n4. Give working code in {languageRequest} if possible\n5. Explain time and space complexity\n\nBe thorough and helpful for an interview context."
                                    },
                                    new {
                                        type = "image_url",
                                        image_url = new { url = $"data:image/png;base64,{base64Image}" }
                                    }
                                }
                            }
                        },
                        max_tokens = 2000,
                        stream = true
                    };
                    
                    var json = System.Text.Json.JsonSerializer.Serialize(requestBody);
                    var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                    var request = new HttpRequestMessage(HttpMethod.Post, OpenAIApiUrl) { Content = content };

                    using var response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
                    response.EnsureSuccessStatusCode();
                    using var stream = await response.Content.ReadAsStreamAsync();
                    using var reader = new StreamReader(stream);
                    
                    string partial = "";
                    bool isFirstToken = true;
                    
                    while (!reader.EndOfStream)
                    {
                        var line = await reader.ReadLineAsync();
                        if (string.IsNullOrWhiteSpace(line) || !line.StartsWith("data:")) continue;
                        
                        var jsonLine = line.Substring(5).Trim();
                        if (jsonLine == "[DONE]") break;
                        
                        try
                        {
                            using var doc = System.Text.Json.JsonDocument.Parse(jsonLine);
                            if (doc.RootElement.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
                            {
                                var choice = choices[0];
                                if (choice.TryGetProperty("delta", out var delta) && delta.TryGetProperty("content", out var contentProp))
                                {
                                    var deltaText = contentProp.GetString();
                                    if (!string.IsNullOrEmpty(deltaText))
                                    {
                                        partial += deltaText;
                                        
                                        Dispatcher.Invoke(() =>
                                        {
                                            // Remove last partial message if present
                                            if (_chatMessages.Count > 0 && _chatMessages[^1].Message.StartsWith("🦾 Vision:"))
                                                _chatMessages.RemoveAt(_chatMessages.Count - 1);
                                            
                                            AddChatMessage($"🦾 Vision: {partial}", "#E8F5E8", "#2E7D32");
                                            
                                            // Auto-scroll after first token
                                            if (isFirstToken)
                                            {
                                                ChatScrollViewer.ScrollToEnd();
                                                isFirstToken = false;
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        catch { /* Ignore parse errors */ }
                    }
                }
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => AddChatMessage($"❌ Vision analysis error: {ex.Message}", "#FFEBEE", "Red"));
            }
        }        
    }
}
