using NAudio.Wave;
using Microsoft.CognitiveServices.Speech;
using Microsoft.CognitiveServices.Speech.Audio;
using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace ScreenShareBlocker
{
    public class AudioTranscriptionService : IDisposable
    {
        private WasapiLoopbackCapture? _capture;
        private SpeechRecognizer? _speechRecognizer;
        private PushAudioInputStream? _pushStream;
        private AudioConfig? _audioConfig;
        private WaveFormat? _targetFormat;
        private readonly ConcurrentQueue<byte[]> _audioQueue = new();
        private CancellationTokenSource? _processingCts;
        private Task? _processingTask;
        private bool _isRunning;
        private readonly Action<string>? _logAction;
        private readonly Action<string>? _transcriptAction;

        // You may want to inject keys/configs or use a config file
        private readonly string _speechKey;
        private readonly string _speechRegion;
        private readonly Action<string>? _finalTranscriptAction;
        private string _latestFinalTranscription = string.Empty;
        
        public string LatestFinalTranscription => _latestFinalTranscription;
        
        public AudioTranscriptionService(string speechKey,
         string speechRegion,
          Action<string>? logAction = null,
          Action<string>? transcriptAction = null,
          Action<string>? finalTranscriptAction = null)
        {
            _speechKey = speechKey;
            _speechRegion = speechRegion;
            _logAction = logAction;
            _transcriptAction = transcriptAction;
            _finalTranscriptAction = finalTranscriptAction;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;
            _isRunning = true;
            _processingCts = new CancellationTokenSource();
            try
            {
                Log("Initializing Azure Speech...");
                InitializeAzureSpeech();
                Log("Azure Speech initialized.");
                Log("Initializing Audio Capture...");
                InitializeAudioCapture();
                Log("Audio Capture initialized.");
                _processingTask = ProcessAudioQueueAsync(_processingCts.Token);
                _capture?.StartRecording();
                await _speechRecognizer!.StartContinuousRecognitionAsync();
                Log("Audio transcription service started.");
            }
            catch (Exception ex)
            {
                Log($"Error starting service: {ex.Message}");
                _isRunning = false;
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning) return;
            _isRunning = false;
            try
            {
                Log("Stopping audio transcription service...");
                _processingCts?.Cancel();
                _capture?.StopRecording();
                if (_speechRecognizer != null)
                    await _speechRecognizer.StopContinuousRecognitionAsync();
                _pushStream?.Close();
                if (_processingTask != null)
                    await _processingTask;
                Log("Audio transcription service stopped.");
            }
            catch (Exception ex)
            {
                Log($"Error stopping service: {ex.Message}");
            }
        }

        private void InitializeAzureSpeech()
        {
            var speechConfig = SpeechConfig.FromSubscription(_speechKey, _speechRegion);
            speechConfig.SpeechRecognitionLanguage = "en-US";
            speechConfig.SetProperty(PropertyId.Speech_SegmentationSilenceTimeoutMs, "2000");
            _pushStream = AudioInputStream.CreatePushStream(AudioStreamFormat.GetWaveFormatPCM(16000, 16, 1));
            _audioConfig = AudioConfig.FromStreamInput(_pushStream);
            _speechRecognizer = new SpeechRecognizer(speechConfig, _audioConfig);
            _speechRecognizer.Recognizing += (s, e) =>
            {
                if (!string.IsNullOrEmpty(e.Result.Text))
                {
                    Transcript($"Live: {e.Result.Text}");
                }
            };
            _speechRecognizer.Recognized += (s, e) =>
            {
                if (e.Result.Reason == ResultReason.RecognizedSpeech && !string.IsNullOrEmpty(e.Result.Text))
                {
                    Transcript($"Final: {e.Result.Text}");
                    _latestFinalTranscription = e.Result.Text;
                    _finalTranscriptAction?.Invoke(e.Result.Text);
                }
            };
            _speechRecognizer.Canceled += (s, e) =>
            {
                Log($"Speech recognition canceled: {e.Reason}, {e.ErrorDetails}");
            };
            _speechRecognizer.SessionStopped += (s, e) =>
            {
                Log("Speech recognition session stopped");
            };
        }

        private void InitializeAudioCapture()
        {
            _capture = new WasapiLoopbackCapture();
            _targetFormat = new WaveFormat(16000, 16, 1);
            _capture.DataAvailable += OnDataAvailable;
            _capture.RecordingStopped += (s, e) =>
            {
                Log("Audio recording stopped");
                if (e.Exception != null)
                {
                    Log($"Recording stopped due to error: {e.Exception.Message}");
                }
            };
        }

        private void OnDataAvailable(object? sender, WaveInEventArgs e)
        {
            if (e.BytesRecorded > 0)
            {
                var audioData = new byte[e.BytesRecorded];
                Array.Copy(e.Buffer, audioData, e.BytesRecorded);
                _audioQueue.Enqueue(audioData);
            }
        }

        private async Task ProcessAudioQueueAsync(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    if (_audioQueue.TryDequeue(out var audioData))
                    {
                        var convertedData = ConvertAudioFormat(audioData, _capture!.WaveFormat, _targetFormat!);
                        if (convertedData?.Length > 0)
                        {
                            _pushStream?.Write(convertedData);
                        }
                    }
                    else
                    {
                        await Task.Delay(10, cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException) { }
            catch (Exception ex)
            {
                Log($"Error processing audio queue: {ex.Message}");
            }
        }

        private byte[] ConvertAudioFormat(byte[] sourceAudio, WaveFormat sourceFormat, WaveFormat targetFormat)
        {
            var sourceSampleRate = sourceFormat.SampleRate;
            var targetSampleRate = targetFormat.SampleRate;
            var sourceChannels = sourceFormat.Channels;
            var sourceBytesPerSample = sourceFormat.BitsPerSample / 8;
            var sourceSampleCount = sourceAudio.Length / (sourceBytesPerSample * sourceChannels);
            var targetSampleCount = (int)(sourceSampleCount * targetSampleRate / sourceSampleRate);
            var output = new System.Collections.Generic.List<byte>();
            for (int i = 0; i < targetSampleCount; i++)
            {
                var sourceIndex = (double)i * sourceSampleRate / targetSampleRate;
                var sourceIndexInt = (int)sourceIndex;
                if (sourceIndexInt >= sourceSampleCount - 1) break;
                var leftSample = BitConverter.ToSingle(sourceAudio, sourceIndexInt * sourceBytesPerSample * sourceChannels);
                var rightSample = sourceChannels > 1 ?
                    BitConverter.ToSingle(sourceAudio, sourceIndexInt * sourceBytesPerSample * sourceChannels + sourceBytesPerSample) :
                    leftSample;
                var monoSample = (leftSample + rightSample) / 2.0f;
                monoSample = Math.Max(-1.0f, Math.Min(1.0f, monoSample));
                var pcmSample = (short)(monoSample * 32767);
                var pcmBytes = BitConverter.GetBytes(pcmSample);
                output.AddRange(pcmBytes);
            }
            return output.ToArray();
        }

        private void Log(string message)
        {
            _logAction?.Invoke(message);
            Console.WriteLine(message);
        }

        private void Transcript(string message)
        {
            _transcriptAction?.Invoke(message);
            Console.WriteLine(message);
        }

        public void Dispose()
        {
            _capture?.Dispose();
            _speechRecognizer?.Dispose();
            _audioConfig?.Dispose();
            _pushStream?.Dispose();
            _processingCts?.Dispose();
        }
    }
}
