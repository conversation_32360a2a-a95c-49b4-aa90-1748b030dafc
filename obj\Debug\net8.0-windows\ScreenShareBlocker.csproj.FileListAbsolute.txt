C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\ScreenShareBlocker.exe
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\ScreenShareBlocker.deps.json
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\ScreenShareBlocker.runtimeconfig.json
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\ScreenShareBlocker.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\ScreenShareBlocker.pdb
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Azure.Core.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\NAudio.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\NAudio.Asio.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\NAudio.Core.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\NAudio.Midi.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\NAudio.Wasapi.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\NAudio.WinForms.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\NAudio.WinMM.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\Newtonsoft.Json.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\System.ClientModel.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\System.Memory.Data.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libpal_azure_c_shared.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libpal_azure_c_shared.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libpal_azure_c_shared.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libpal_azure_c_shared_openssl3.so
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\Users\<USER>\InterviewAI-WPF\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.csproj.AssemblyReference.cache
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\MainWindow.baml
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\TokenInputWindow.baml
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\MainWindow.g.cs
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\TokenInputWindow.g.cs
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\App.g.cs
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker_MarkupCompile.cache
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.g.resources
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.AssemblyInfoInputs.cache
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.AssemblyInfo.cs
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.csproj.CoreCompileInputs.cache
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.sourcelink.json
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenSh.3EAD780F.Up2Date
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.dll
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\refint\ScreenShareBlocker.dll
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.pdb
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ScreenShareBlocker.genruntimeconfig.cache
C:\Users\<USER>\InterviewAI-WPF\obj\Debug\net8.0-windows\ref\ScreenShareBlocker.dll
