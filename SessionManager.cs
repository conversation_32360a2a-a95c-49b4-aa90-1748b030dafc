using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using Newtonsoft.Json;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace ScreenShareBlocker
{
    public class SessionManager
    {
        private const string SECRET_KEY = "YourSecretKeyForJWTTokenValidation123456789"; // In production, use secure key management
        private UserData? _userData;
        private string _currentToken = string.Empty;

        public UserData? UserData => _userData;
        public bool HasValidSession => _userData != null && _userData.IsSessionActive;

        public bool ValidateToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(SECRET_KEY);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,   // adjust for production
                    ValidateAudience = false, // adjust for production
                    ClockSkew = TimeSpan.Zero
                };

                // Validate signature + lifetime etc.
                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

                // Ensure algorithm is HMAC-SHA256 (HS256) as produced by jose (or allow other HMAC algs if needed)
                if (validatedToken is JwtSecurityToken jwt)
                {
                    var alg = jwt.Header.Alg ?? string.Empty;
                    if (!string.Equals(alg, SecurityAlgorithms.HmacSha256, StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(alg, "HS256", StringComparison.OrdinalIgnoreCase))
                    {
                        // algorithm mismatch
                        System.Diagnostics.Debug.WriteLine($"Unexpected JWT alg: {alg}");
                        return false;
                    }
                }

                // Extract user data from validated claims
                _userData = ExtractUserDataFromClaims(principal.Claims);
                _currentToken = token;

                return _userData?.IsSessionActive ?? false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Token validation failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Inspect token payload without validation (useful for debugging).
        /// </summary>
        public JwtSecurityToken ReadTokenWithoutValidation(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            return handler.ReadJwtToken(token);
        }        

        private UserData ExtractUserDataFromClaims(IEnumerable<Claim> claims)
        {
            var userData = new UserData();
            
            foreach (var claim in claims)
            {
                    // Replace the "skills" case in ExtractUserDataFromClaims with this more robust version
                switch (claim.Type)
                {
                    case "userId":
                        userData.UserId = claim.Value;
                        break;
                    case "name":
                        userData.Name = claim.Value;
                        break;
                    case "email":
                        userData.Email = claim.Value;
                        break;
                    case "resume":
                        userData.Resume = claim.Value;
                        break;
                    case "skills":
                        try
                        {
                            // Try to parse as JSON array
                            if (claim.Value.TrimStart().StartsWith("["))
                            {
                                userData.Skills = JsonConvert.DeserializeObject<List<string>>(claim.Value) ?? new List<string>();
                            }
                            else
                            {
                                // If not a JSON array, treat as comma-separated string
                                userData.Skills = claim.Value.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                                             .Select(s => s.Trim())
                                                             .ToList();
                            }
                        }
                        catch
                        {
                            userData.Skills = new List<string>();
                        }
                        break;
                    case "sessionStart":
                        if (DateTime.TryParse(claim.Value, out DateTime sessionStart))
                            userData.SessionStart = sessionStart.ToUniversalTime();
                        break;
                    case "sessionDurationMinutes":
                        if (int.TryParse(claim.Value, out int duration))
                            userData.SessionDurationMinutes = duration;
                        break;
                }
            }
            
            return userData;
        }


        private static string DecryptXorBase64(string encrypted, string password)
        {
            if (string.IsNullOrEmpty(encrypted)) return string.Empty;
            if (string.IsNullOrEmpty(password)) throw new ArgumentException("password required", nameof(password));

            // base64 -> raw bytes
            var bytes = Convert.FromBase64String(encrypted);

            // In JS unescape results in 1-byte-per-char string (Latin1). Recreate that by decoding bytes as ISO-8859-1 (Latin1)
            var latin1 = Encoding.GetEncoding("ISO-8859-1").GetString(bytes);

            var pwdBytes = Encoding.UTF8.GetBytes(password);
            var outChars = new char[latin1.Length];

            for (int i = 0; i < latin1.Length; i++)
            {
                // XOR the byte value with password byte (matches JS xor on charCode)
                int b = latin1[i];
                int k = pwdBytes[i % pwdBytes.Length];
                outChars[i] = (char)(b ^ k);
            }

            return new string(outChars);
        }


        public bool ValidateEncryptedToken(string encryptedToken, string password)
        {
            try
            {
                var decrypted = DecryptXorBase64(encryptedToken, password);
                // If decrypted payload is JSON and contains a "token" property, use that as the JWT.
                string jwtCandidate = decrypted;
                try
                {
                    var j = JObject.Parse(decrypted);
                    if (j["token"] != null)
                        jwtCandidate = j["token"]!.ToString();
                }
                catch
                {
                    // not JSON — assume decrypted string is the JWT itself
                }
                return ValidateToken(jwtCandidate);
            }
            catch (Exception ex)
            {
                // Console.WriteLine($"ValidateEncryptedToken failed: {ex.Message}");
                return false;
            }
        }

        public string GetSessionStatus()
        {
            if (_userData == null)
                return "No active session";

            if (!_userData.IsSessionActive)
                return "Session expired";

            var remaining = _userData.RemainingTime;
            return $"Session active - {remaining.Hours:D2}:{remaining.Minutes:D2}:{remaining.Seconds:D2} remaining";
        }

        public void EndSession()
        {
            _userData = null;
            _currentToken = string.Empty;
        }
    }
}
