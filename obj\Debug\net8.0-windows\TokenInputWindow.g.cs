﻿#pragma checksum "..\..\..\TokenInputWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FF53CDF459893D8811E0927712F918B089C56B24"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ScreenShareBlocker {
    
    
    /// <summary>
    /// TokenInputWindow
    /// </summary>
    public partial class TokenInputWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 52 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TokenTextBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ActiveTokenTextBox;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpiredTokenTextBox;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FutureTokenTextBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ValidateButton;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearButton;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\TokenInputWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExitButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ScreenShareBlocker;component/tokeninputwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\TokenInputWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TokenTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            
            #line 89 "..\..\..\TokenInputWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SampleToken_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ActiveTokenTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            
            #line 115 "..\..\..\TokenInputWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SampleToken_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ExpiredTokenTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            
            #line 141 "..\..\..\TokenInputWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SampleToken_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FutureTokenTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ValidateButton = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\TokenInputWindow.xaml"
            this.ValidateButton.Click += new System.Windows.RoutedEventHandler(this.ValidateButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ClearButton = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\TokenInputWindow.xaml"
            this.ClearButton.Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ExitButton = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\TokenInputWindow.xaml"
            this.ExitButton.Click += new System.Windows.RoutedEventHandler(this.ExitButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

