<Window x:Class="ScreenShareBlocker.TokenInputWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Interview AI Assistant - Session Token" 
        Height="550" Width="650"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#1E1E1E">
    
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        <Style TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="25">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,25">
            <TextBlock Text="🤖 Interview AI Assistant" 
                       FontSize="28" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center"
                       Foreground="#2196F3"/>
            <TextBlock Text="Please enter your session token to access the interview assistant" 
                       FontSize="14" 
                       HorizontalAlignment="Center"
                       Foreground="#B0BEC5"
                       Margin="0,8,0,0"/>
        </StackPanel>
        
        <!-- Token Input -->
        <StackPanel Grid.Row="1" Margin="0,0,0,15">
            <TextBlock Text="Session Token:" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="White"/>
            <TextBox x:Name="TokenTextBox" 
                     Height="120"
                     TextWrapping="Wrap"
                     VerticalScrollBarVisibility="Auto"
                     AcceptsReturn="False"
                     FontFamily="Consolas"
                     FontSize="11"
                     Background="#2C2C2C"
                     Foreground="#E0E0E0"
                     BorderBrush="#404040"
                     BorderThickness="1"/>
        </StackPanel>
        
        <!-- Sample Tokens Section -->
        <Border Grid.Row="2" 
                Background="#252525" 
                BorderBrush="#404040" 
                BorderThickness="1" 
                CornerRadius="6" 
                Padding="15" 
                Margin="0,0,0,15">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <TextBlock Text="💡 Sample Tokens for Testing" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Margin="0,0,0,15" 
                               Foreground="White"/>
                    <TextBlock Text="Click any token below to use it for testing:" 
                               Margin="0,0,0,15" 
                               FontStyle="Italic"
                               Foreground="#B0BEC5"/>
                    
                    <!-- Active Token -->
                    <Border BorderBrush="#4CAF50" BorderThickness="2" 
                            CornerRadius="8" Margin="0,8" Padding="12"
                            Background="#1B5E20" Cursor="Hand"
                            MouseLeftButtonDown="SampleToken_Click" Tag="active">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <TextBlock Text="🟢 ACTIVE SESSION" 
                                           FontWeight="Bold" 
                                           Foreground="#81C784"/>
                                <TextBlock Text=" (John Doe - Started 10 min ago, 60 min duration)" 
                                           Foreground="#E8F5E8" 
                                           Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBox x:Name="ActiveTokenTextBox" 
                                     IsReadOnly="True" 
                                     Height="70"
                                     TextWrapping="Wrap"
                                     FontFamily="Consolas" 
                                     FontSize="9"
                                     Background="#2E7D32"
                                     Foreground="#E8F5E8"
                                     BorderThickness="0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Expired Token -->
                    <Border BorderBrush="#F44336" BorderThickness="2" 
                            CornerRadius="8" Margin="0,8" Padding="12"
                            Background="#B71C1C" Cursor="Hand"
                            MouseLeftButtonDown="SampleToken_Click" Tag="expired">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <TextBlock Text="🔴 EXPIRED SESSION" 
                                           FontWeight="Bold" 
                                           Foreground="#EF9A9A"/>
                                <TextBlock Text=" (Jane Smith - Started 90 min ago, 30 min duration)" 
                                           Foreground="#FFEBEE" 
                                           Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBox x:Name="ExpiredTokenTextBox" 
                                     IsReadOnly="True" 
                                     Height="70"
                                     TextWrapping="Wrap"
                                     FontFamily="Consolas" 
                                     FontSize="9"
                                     Background="#D32F2F"
                                     Foreground="#FFEBEE"
                                     BorderThickness="0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Future Token -->
                    <Border BorderBrush="#FF9800" BorderThickness="2" 
                            CornerRadius="8" Margin="0,8" Padding="12"
                            Background="#E65100" Cursor="Hand"
                            MouseLeftButtonDown="SampleToken_Click" Tag="future">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <TextBlock Text="🟡 FUTURE SESSION" 
                                           FontWeight="Bold" 
                                           Foreground="#FFB74D"/>
                                <TextBlock Text=" (Mike Johnson - Starts in 30 min, 45 min duration)" 
                                           Foreground="#FFF3E0" 
                                           Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBox x:Name="FutureTokenTextBox" 
                                     IsReadOnly="True" 
                                     Height="70"
                                     TextWrapping="Wrap"
                                     FontFamily="Consolas" 
                                     FontSize="9"
                                     Background="#F57C00"
                                     Foreground="#FFF3E0"
                                     BorderThickness="0"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- Status Message -->
        <TextBlock x:Name="StatusTextBlock" 
                   Grid.Row="3"
                   Margin="0,10,0,15"
                   HorizontalAlignment="Center"
                   FontWeight="Medium"
                   FontSize="13"/>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="4" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            <Button x:Name="ValidateButton" 
                    Content="🚀 Start Session" 
                    Click="ValidateButton_Click"
                    Background="#2196F3" 
                    Foreground="White"
                    IsDefault="True"/>
            <Button x:Name="ClearButton" 
                    Content="🗑️ Clear" 
                    Click="ClearButton_Click"
                    Background="#607D8B" 
                    Foreground="White"/>
            <Button x:Name="ExitButton" 
                    Content="❌ Exit" 
                    Click="ExitButton_Click"
                    Background="#F44336" 
                    Foreground="White"
                    IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
