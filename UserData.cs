using System;
using System.Collections.Generic;

namespace ScreenShareBlocker
{
    public class UserData
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Resume { get; set; } = string.Empty;
        public List<string> Skills { get; set; } = new List<string>();
        public DateTime SessionStart { get; set; }
        public int SessionDurationMinutes { get; set; }
        public DateTime SessionEnd => SessionStart.AddMinutes(SessionDurationMinutes);
        
        public bool IsSessionActive => DateTime.UtcNow >= SessionStart && DateTime.UtcNow <= SessionEnd;
        
        public TimeSpan RemainingTime => SessionEnd > DateTime.UtcNow ? SessionEnd - DateTime.UtcNow : TimeSpan.Zero;
    }
}
