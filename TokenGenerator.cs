using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace ScreenShareBlocker
{
    public static class TokenGenerator
    {
        private const string SECRET_KEY = "YourSecretKeyForJWTTokenValidation123456789";

        public static string GenerateDummyToken(string scenario = "active")
        {
            var key = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(SECRET_KEY));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = scenario switch
            {
                "active" => GetActiveSessionClaims(),
                "expired" => GetExpiredSessionClaims(),
                "future" => GetFutureSessionClaims(),
                _ => GetActiveSessionClaims()
            };

            var token = new JwtSecurityToken(
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(120), // Token expiry (different from session expiry)
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        private static List<Claim> GetActiveSessionClaims()
        {
            var sessionStart = DateTime.UtcNow.AddMinutes(-10); // Started 10 minutes ago
            var skills = new List<string> { "C#", "WPF", ".NET", "SQL", "JavaScript", "React", "AI Assistant", "Interview Prep" };
            
            return new List<Claim>
            {
                new Claim("userId", "USR_12345"),
                new Claim("name", "John Doe"),
                new Claim("email", "<EMAIL>"),
                new Claim("resume", "Experienced software developer with 5+ years in .NET technologies, specializing in desktop applications, web development, and AI-powered interview assistance. Strong background in C#, WPF, Azure Cognitive Services, and modern web frameworks."),
                new Claim("skills", JsonConvert.SerializeObject(skills)),
                new Claim("sessionStart", sessionStart.ToString("O")),
                new Claim("sessionDurationMinutes", "60") // 60 minute session
            };
        }

        private static List<Claim> GetExpiredSessionClaims()
        {
            var sessionStart = DateTime.UtcNow.AddMinutes(-90); // Started 90 minutes ago
            var skills = new List<string> { "Python", "Django", "PostgreSQL", "Docker", "Machine Learning" };
            
            return new List<Claim>
            {
                new Claim("userId", "USR_67890"),
                new Claim("name", "Jane Smith"),
                new Claim("email", "<EMAIL>"),
                new Claim("resume", "Full-stack developer with expertise in Python backend development, machine learning, and modern web technologies."),
                new Claim("skills", JsonConvert.SerializeObject(skills)),
                new Claim("sessionStart", sessionStart.ToString("O")),
                new Claim("sessionDurationMinutes", "30") // 30 minute session (expired)
            };
        }

        private static List<Claim> GetFutureSessionClaims()
        {
            var sessionStart = DateTime.UtcNow.AddMinutes(30); // Starts in 30 minutes
            var skills = new List<string> { "Java", "Spring Boot", "Microservices", "AWS", "Kubernetes" };
            
            return new List<Claim>
            {
                new Claim("userId", "USR_11111"),
                new Claim("name", "Mike Johnson"),
                new Claim("email", "<EMAIL>"),
                new Claim("resume", "Senior Java developer with cloud architecture experience, microservices expertise, and extensive knowledge in scalable system design."),
                new Claim("skills", JsonConvert.SerializeObject(skills)),
                new Claim("sessionStart", sessionStart.ToString("O")),
                new Claim("sessionDurationMinutes", "45") // 45 minute session
            };
        }

        public static void PrintSampleTokens()
        {
            Console.WriteLine("=== SAMPLE JWT TOKENS FOR INTERVIEW AI ASSISTANT ===\n");
            
            Console.WriteLine("1. ACTIVE SESSION TOKEN (Started 10 min ago, 60 min duration):");
            Console.WriteLine(GenerateDummyToken("active"));
            Console.WriteLine();
            
            Console.WriteLine("2. EXPIRED SESSION TOKEN (Started 90 min ago, 30 min duration):");
            Console.WriteLine(GenerateDummyToken("expired"));
            Console.WriteLine();
            
            Console.WriteLine("3. FUTURE SESSION TOKEN (Starts in 30 min, 45 min duration):");
            Console.WriteLine(GenerateDummyToken("future"));
            Console.WriteLine();
        }
    }
}
