<!-- Website Download Section for Interview AI Assistant -->
<div class="download-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px; border-radius: 15px; color: white; text-align: center; margin: 20px 0;">
    
    <h2 style="font-size: 2.5em; margin-bottom: 10px;">🤖 Download Interview AI Assistant</h2>
    <p style="font-size: 1.2em; margin-bottom: 30px; opacity: 0.9;">
        Powerful desktop application with real-time AI assistance for your interviews
    </p>
    
    <!-- Main Download Button -->
    <div style="margin-bottom: 30px;">
        <a href="/downloads/InterviewAI-Assistant-Complete.zip" 
           class="download-btn"
           style="display: inline-block; background: #4CAF50; color: white; padding: 15px 40px; 
                  text-decoration: none; border-radius: 50px; font-size: 1.3em; font-weight: bold;
                  box-shadow: 0 4px 15px rgba(0,0,0,0.2); transition: all 0.3s ease;
                  border: 3px solid #45a049;">
            📥 Download Now (1.8 MB)
        </a>
    </div>
    
    <!-- Features Grid -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;">
        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
            <h3>🎧 Real-time Audio</h3>
            <p>Speech recognition and live transcription</p>
        </div>
        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
            <h3>🤖 AI Assistant</h3>
            <p>GPT-4 powered interview guidance</p>
        </div>
        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
            <h3>📸 Screen Analysis</h3>
            <p>Coding challenge problem solving</p>
        </div>
        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px;">
            <h3>🔒 Privacy Protected</h3>
            <p>Hidden from screen sharing</p>
        </div>
    </div>
    
    <!-- Installation Steps -->
    <div style="background: rgba(255,255,255,0.1); padding: 25px; border-radius: 10px; margin: 20px 0;">
        <h3 style="margin-bottom: 15px;">📋 Installation Steps</h3>
        <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
            <div style="margin: 10px;">
                <div style="background: #2196F3; width: 40px; height: 40px; border-radius: 50%; 
                           display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">1</div>
                <p><strong>Download</strong><br>Click download button</p>
            </div>
            <div style="margin: 10px;">
                <div style="background: #FF9800; width: 40px; height: 40px; border-radius: 50%; 
                           display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">2</div>
                <p><strong>Extract</strong><br>Unzip the file</p>
            </div>
            <div style="margin: 10px;">
                <div style="background: #4CAF50; width: 40px; height: 40px; border-radius: 50%; 
                           display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">3</div>
                <p><strong>Run</strong><br>Double-click the EXE</p>
            </div>
        </div>
    </div>
    
    <!-- System Requirements -->
    <div style="text-align: left; background: rgba(0,0,0,0.2); padding: 20px; border-radius: 10px; margin: 20px 0;">
        <h4>💻 System Requirements:</h4>
        <ul style="margin: 10px 0; padding-left: 20px;">
            <li>Windows 10 (version 2004+) or Windows 11</li>
            <li>4GB RAM minimum (8GB recommended)</li>
            <li>300MB free disk space</li>
            <li>Internet connection for AI features</li>
            <li><strong>No .NET installation required!</strong></li>
        </ul>
    </div>
    
    <!-- Security Note -->
    <div style="background: rgba(255,193,7,0.2); padding: 15px; border-radius: 8px; border-left: 4px solid #FFC107;">
        <p style="margin: 0; font-size: 0.9em;">
            <strong>🛡️ Security Note:</strong> Windows may show a security warning for new applications. 
            Click "More info" then "Run anyway" to proceed.
        </p>
    </div>
</div>

<!-- CSS for hover effects -->
<style>
.download-btn:hover {
    background: #45a049 !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
}
</style>
