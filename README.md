# Interview AI Assistant - Integrated Session Management

Your existing **Interview AI Assistant** application has been enhanced with JWT token-based session management. The application now requires users to authenticate with a session token before accessing the interview assistance features.

## 🚀 **Architecture Flow**

1. **Application Startup**: Launches with Token Input Dialog
2. **Token Validation**: User enters JWT token containing session details
3. **Session Check**: Validates token signature and session time window
4. **Main Application**: If session is active, opens the full AI Assistant
5. **Session Monitoring**: Real-time session countdown with automatic expiry

## 🔧 **New Features Added**

### **JWT Session Management**
- `SessionManager.cs` - Validates JWT tokens and manages session state
- `UserData.cs` - User session data model with time validation
- `TokenGenerator.cs` - Generates dummy tokens for testing

### **Token Input Interface**
- `TokenInputWindow.xaml/cs` - Startup dialog for token entry
- Built-in sample tokens for easy testing
- Real-time token validation feedback

### **Enhanced Main Application**
- Session timer with live countdown in window title
- User welcome messages with session details
- Session warnings at 10 and 5 minutes remaining
- Automatic session expiry handling

## 📝 **JWT Token Structure**

The application expects tokens with these claims:
```json
{
  "userId": "USR_12345",
  "name": "John Doe", 
  "email": "<EMAIL>",
  "resume": "User's resume text...",
  "skills": ["C#", "WPF", ".NET", "AI Assistant"],
  "sessionStart": "2025-08-27T07:25:12Z",
  "sessionDurationMinutes": 60
}
```

## 🧪 **Testing the Application**

### **Method 1: Built-in Sample Tokens**
1. Run the application: `dotnet run`
2. Click on any sample token in the interface:
   - **🟢 Active Token**: Grants access to full assistant
   - **🔴 Expired Token**: Shows "session expired" message  
   - **🟡 Future Token**: Shows "session not started yet" message
3. Click "🚀 Start Session"

### **Method 2: Generate Your Own Tokens**
You can call the `TokenGenerator.GenerateDummyToken()` method programmatically:

```csharp
// Generate different token types
var activeToken = TokenGenerator.GenerateDummyToken("active");
var expiredToken = TokenGenerator.GenerateDummyToken("expired"); 
var futureToken = TokenGenerator.GenerateDummyToken("future");
```

## 🎯 **Session Management Features**

### **Real-time Session Monitoring**
- Window title shows countdown: `Interview AI Assistant - 00:45:32 remaining`
- Session warnings at 10 and 5 minutes
- Automatic application closure when session expires

### **Session Data Integration**
- User information displayed in chat on startup
- Resume and skills available for AI context
- Session details logged for reference

## 🔒 **Security Notes**

⚠️ **For Production Use:**
- Replace hardcoded `SECRET_KEY` with secure key management
- Enable JWT issuer and audience validation  
- Use HTTPS for all token transmission
- Consider encrypting sensitive claims
- Implement token refresh mechanisms

## 🌐 **Web Portal Integration**

For integration with `https://interviewsupport-ai.com/home`:

1. **User Login**: User authenticates on web portal
2. **Token Generation**: Server generates JWT with user data and session window
3. **Token Delivery**: Options:
   - Download link with embedded token
   - Secure display for copy/paste
   - Email delivery
4. **Desktop Launch**: User runs WPF application with token

## 🛠️ **Dependencies Added**

```xml
<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

## 🚦 **Getting Started**

1. **Build**: `dotnet build`
2. **Run**: `dotnet run` 
3. **Test**: Use any built-in sample token
4. **Deploy**: Package as usual WPF application

Your Interview AI Assistant now has enterprise-ready session management while maintaining all existing features like audio transcription, screen capture, and AI chat integration!

---

## 🎤 **Existing Features (Preserved)**

### **Audio Transcription & AI Chat**
- Real-time speech recognition using Azure Cognitive Services
- ChatGPT integration for interview assistance
- Live audio transcription with immediate AI analysis

### **Screen Capture & Vision Analysis**
- Screenshot capture and AI vision analysis
- Coding challenge problem solving
- Technical interview question analysis

### **Screen Share Protection**
- Prevents window from appearing in Teams screen shares
- Uses `SetWindowDisplayAffinity` with `WDA_EXCLUDEFROMCAPTURE`
- Compatible with Windows 10 version 2004+

## 📈 **Next Steps**

- [ ] Integrate with your web portal's token generation
- [ ] Add user session analytics
- [ ] Implement token refresh mechanisms
- [ ] Enhanced error handling and logging
- [ ] Production security hardening
