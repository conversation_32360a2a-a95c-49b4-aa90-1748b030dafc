# Sample JWT Tokens for Testing

Copy and paste these tokens into your application for immediate testing:

## 🟢 ACTIVE SESSION TOKEN
**User**: <PERSON> (Started 10 min ago, 60 min duration)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.example_signature_active
```

## 🔴 EXPIRED SESSION TOKEN  
**User**: <PERSON> (Started 90 min ago, 30 min duration)
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.example_signature_expired
```

## 🟡 FUTURE SESSION TOKEN
**User**: <PERSON> (Starts in 30 min, 45 min duration)  
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.example_signature_future
```

## How to Use

1. **Run the application**: `dotnet run`
2. **Copy any token** from above
3. **Paste into the token field** in the application
4. **Click "🚀 Start Session"**
5. **Observe different behaviors**:
   - Active token → Opens main interview assistant
   - Expired token → Shows expired session message
   - Future token → Shows session not started message

## Real Tokens

The application also generates real JWT tokens dynamically with proper signatures. The built-in sample tokens in the interface are fully functional and change based on the current time.
